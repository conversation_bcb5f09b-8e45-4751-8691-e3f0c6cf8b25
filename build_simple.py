import subprocess
import sys
import os

def build_exe():
    """使用PyInstaller打包程序"""
    
    # 检查PyInstaller是否安装
    try:
        import PyInstaller
    except ImportError:
        print("正在安装PyInstaller...")
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pyinstaller'])
    
    # 构建命令
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--onefile',  # 打包成单个文件
        '--console',  # 显示控制台窗口（便于调试）
        '--add-data', 'templates;templates',  # 添加模板文件夹
        '--add-data', 'key.vdf;.',  # 添加密钥文件
        '--add-data', 'settings.ini;.',  # 添加设置文件（如果存在）
        '--hidden-import', 'flask',
        '--hidden-import', 'werkzeug',
        '--hidden-import', 'jinja2',
        '--hidden-import', 'requests',
        '--hidden-import', 'PIL',
        '--hidden-import', 'PIL.Image',
        '--hidden-import', 'pyperclip',
        '--hidden-import', 'cryptography',
        '--hidden-import', 'cryptography.hazmat.primitives.kdf.pbkdf2',
        '--hidden-import', 'cryptography.hazmat.primitives.hashes',
        '--hidden-import', 'cryptography.hazmat.primitives.padding',
        '--hidden-import', 'cryptography.hazmat.primitives.ciphers',
        '--hidden-import', 'cryptography.hazmat.primitives.ciphers.algorithms',
        '--hidden-import', 'cryptography.hazmat.primitives.ciphers.modes',
        '--hidden-import', 'tkinter',
        '--hidden-import', 'tkinter.messagebox',
        '--hidden-import', 'json',
        '--hidden-import', 'datetime',
        '--hidden-import', 'urllib.parse',
        '--hidden-import', 'threading',
        '--hidden-import', 'webbrowser',
        '--name', '商品数据管理工具_修复版',
        'main.py'
    ]
    
    print("开始打包...")
    print("命令:", ' '.join(cmd))
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 打包成功！")
        print("可执行文件位置: dist/商品数据管理工具_修复版.exe")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        print("错误输出:", e.stderr)
        return False

if __name__ == '__main__':
    print("=== 商品数据管理工具简化打包脚本 ===")
    build_exe()
    input("按回车键退出...")