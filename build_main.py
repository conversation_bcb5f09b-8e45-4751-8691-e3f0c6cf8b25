import os
import sys
import time
import shutil
import subprocess
import json
from pathlib import Path

def print_banner():
    """打印启动横幅"""
    print("=" * 60)
    print("🚀 商品数据管理工具 - 自动打包脚本")
    print("=" * 60)

def check_dependencies():
    """检查必要的依赖库"""
    package_imports = {
        'flask': 'flask',
        'requests': 'requests', 
        'pillow': 'PIL',
        'pyperclip': 'pyperclip',
        'cryptography': 'cryptography',
        'pyinstaller': 'PyInstaller'
    }
    
    missing_packages = []
    print("📦 检查依赖库...")
    
    for package_name, import_name in package_imports.items():
        try:
            __import__(import_name)
            print(f"  ✅ {package_name}")
        except ImportError:
            missing_packages.append(package_name)
            print(f"  ❌ {package_name} - 缺失")
    
    if missing_packages:
        print(f"\n⚠️  缺少以下依赖库: {', '.join(missing_packages)}")
        print("请先安装缺失的依赖库:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖库检查完成\n")
    return True

def check_project_files():
    """检查项目文件完整性"""
    print("📁 检查项目文件...")
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    required_files = {
        'main.py': '主程序文件',
        'templates/index.html': '模板文件',
        'key.vdf': '授权文件'
    }
    
    missing_files = []
    for file_path, description in required_files.items():
        full_path = os.path.join(current_dir, file_path)
        if os.path.exists(full_path):
            print(f"  ✅ {description}: {file_path}")
        else:
            missing_files.append(f"{description}: {file_path}")
            print(f"  ❌ {description}: {file_path} - 缺失")
    
    if missing_files:
        print(f"\n⚠️  缺少以下项目文件:")
        for file in missing_files:
            print(f"    - {file}")
        return False
    
    print("✅ 项目文件检查完成\n")
    return True

def get_build_info():
    """获取构建信息"""
    try:
        # 尝试获取Git信息
        git_hash = subprocess.check_output(['git', 'rev-parse', '--short', 'HEAD'], 
                                         stderr=subprocess.DEVNULL).decode().strip()
        git_branch = subprocess.check_output(['git', 'rev-parse', '--abbrev-ref', 'HEAD'], 
                                           stderr=subprocess.DEVNULL).decode().strip()
        return f"{git_branch}-{git_hash}"
    except:
        return time.strftime("%Y%m%d_%H%M%S")

def main():
    """主函数"""
    print_banner()
    print(f"🐍 当前Python版本: {sys.version}")
    
    # 获取当前目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    print(f"📂 当前工作目录: {current_dir}\n")
    
    # 检查依赖库
    if not check_dependencies():
        input("按回车键退出...")
        sys.exit(1)
    
    # 检查项目文件
    if not check_project_files():
        input("按回车键退出...")
        sys.exit(1)
    
    # 定义主程序路径
    main_script = os.path.join(current_dir, 'main.py')
    
    # 获取构建信息
    build_info = get_build_info()
    exe_name = f"商品数据管理工具_v{build_info}"
    
    print(f"🔨 开始打包程序...")
    print(f"📝 程序名称: {exe_name}")
    
    # 定义打包参数
    params = [
        main_script,  # 主程序
        f'--name={exe_name}',  # 生成的exe名称
        '--onefile',  # 打包成单个exe文件
        '--console',  # 显示控制台窗口
        '--noconfirm',  # 不询问确认
        '--clean',  # 清理临时文件
        '--optimize=2',  # 优化字节码
        '--strip',  # 去除调试符号
        
        # 隐藏导入
        '--hidden-import=flask',
        '--hidden-import=werkzeug',
        '--hidden-import=jinja2',
        '--hidden-import=PIL',
        '--hidden-import=PIL.Image',
        '--hidden-import=requests',
        '--hidden-import=urllib3',
        '--hidden-import=pyperclip',
        '--hidden-import=cryptography',
        '--hidden-import=cryptography.hazmat.primitives.kdf.pbkdf2',
        '--hidden-import=cryptography.hazmat.primitives.ciphers',
        '--hidden-import=tkinter',
        '--hidden-import=tkinter.messagebox',
        '--hidden-import=webbrowser',
        '--hidden-import=threading',
        
        # 添加数据文件
        '--add-data=templates;templates',
        '--add-data=key.vdf;.',
    ]
    
    # 检查并添加其他资源文件
    optional_files = ['settings.ini', 'config.json', 'README.md']
    for file in optional_files:
        if os.path.exists(os.path.join(current_dir, file)):
            params.append(f'--add-data={file};.')
            print(f"  📄 添加资源文件: {file}")
    
    print(f"\n⚙️  打包参数:")
    for param in params:
        if param.startswith('--'):
            print(f"  {param}")
    
    print(f"\n🚀 开始执行PyInstaller打包...")
    start_time = time.time()
    
    try:
        # 使用subprocess调用pyinstaller命令
        cmd = ['pyinstaller'] + params[1:]  # 跳过main_script，因为它会作为第一个参数
        cmd.insert(1, params[0])  # 插入main_script作为第一个参数
        
        print(f"🔧 执行命令: pyinstaller {os.path.basename(params[0])} --name={exe_name}...")
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode != 0:
            print(f"❌ PyInstaller执行失败:")
            print(f"错误输出: {result.stderr}")
            if result.stdout:
                print(f"标准输出: {result.stdout}")
            raise Exception(f"PyInstaller返回码: {result.returncode}")
        
        print("✅ PyInstaller执行完成")
        
        # 检查是否成功生成文件
        dist_dir = os.path.join(current_dir, 'dist')
        exe_path = os.path.join(dist_dir, f'{exe_name}.exe')
        
        if os.path.exists(exe_path):
            elapsed_time = time.time() - start_time
            file_size = os.path.getsize(exe_path) / (1024*1024)
            
            print(f"\n🎉 打包成功!")
            print(f"⏱️  用时: {elapsed_time:.2f}秒")
            print(f"📁 可执行文件: {exe_path}")
            print(f"📊 文件大小: {file_size:.2f} MB")
            
            # 清理临时文件
            build_dir = os.path.join(current_dir, 'build')
            spec_files = [f for f in os.listdir(current_dir) if f.endswith('.spec')]
            
            if os.path.exists(build_dir):
                print("🧹 清理临时文件...")
                shutil.rmtree(build_dir)
            
            for spec_file in spec_files:
                os.remove(os.path.join(current_dir, spec_file))
                print(f"  🗑️  删除: {spec_file}")
            
            # 生成构建信息文件
            build_info_data = {
                "build_time": time.strftime("%Y-%m-%d %H:%M:%S"),
                "python_version": sys.version,
                "file_size_mb": round(file_size, 2),
                "build_duration_seconds": round(elapsed_time, 2),
                "exe_name": exe_name
            }
            
            info_file = os.path.join(dist_dir, "build_info.json")
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(build_info_data, f, ensure_ascii=False, indent=2)
            
            print(f"📋 构建信息已保存到: {info_file}")
            
            print("\n" + "=" * 60)
            print("📋 使用说明:")
            print("1. 可执行文件已生成在 dist 目录中")
            print("2. 程序启动时会自动验证授权文件 key.vdf")
            print("3. 验证通过后会自动打开浏览器访问 http://localhost:8726")
            print("4. 控制台窗口会显示Flask应用的运行日志")
            print("5. 确保 key.vdf 授权文件与exe在同一目录")
            print("6. 首次运行可能需要一些时间来解压内部文件")
            print("=" * 60)
            
        else:
            print(f"\n❌ 打包失败: 未找到生成的可执行文件")
            print(f"预期路径: {exe_path}")
            print("请检查上方日志获取详细错误信息")
            
    except Exception as e:
        print(f"\n❌ 打包过程中出错: {str(e)}")
        print("\n🔧 可能的解决方案:")
        print("1. 确保所有依赖库都已正确安装")
        print("2. 检查main.py中是否有语法错误")
        print("3. 尝试在命令行中直接运行: python main.py")
        print("4. 检查是否缺少必要的模块导入")
        print("5. 尝试重新安装PyInstaller: pip install --upgrade pyinstaller")
    
    print(f"\n程序结束")
    input("按回车键退出...")

if __name__ == "__main__":
    main()