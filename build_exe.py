import os
import sys
import subprocess
import shutil
from pathlib import Path

def install_requirements():
    """安装所需的依赖包"""
    requirements = [
        'flask',
        'requests',
        'pillow',
        'pyperclip',
        'cryptography',
        'pyinstaller'
    ]
    
    print("正在安装依赖包...")
    for package in requirements:
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✅ {package} 安装成功")
        except subprocess.CalledProcessError:
            print(f"❌ {package} 安装失败")
            return False
    return True

def create_spec_file():
    """创建PyInstaller规格文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('templates', 'templates'),
        ('key.vdf', '.'),
        ('settings.ini', '.')
    ],
    hiddenimports=[
        'flask',
        'requests',
        'PIL',
        'PIL.Image',
        'pyperclip',
        'cryptography',
        'cryptography.hazmat.primitives.kdf.pbkdf2',
        'cryptography.hazmat.primitives.hashes',
        'cryptography.hazmat.primitives.padding',
        'cryptography.hazmat.primitives.ciphers',
        'cryptography.hazmat.primitives.ciphers.algorithms',
        'cryptography.hazmat.primitives.ciphers.modes',
        'tkinter',
        'tkinter.messagebox',
        'json',
        'os',
        'datetime',
        'urllib.parse',
        'io',
        'threading',
        'queue',
        'time',
        'tempfile',
        'zipfile',
        'webbrowser'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='商品数据管理工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None
)
'''
    
    with open('main.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    print("✅ 规格文件创建成功")

def build_exe():
    """构建可执行文件"""
    print("开始构建可执行文件...")
    
    # 清理之前的构建文件
    if os.path.exists('build'):
        shutil.rmtree('build')
    if os.path.exists('dist'):
        shutil.rmtree('dist')
    
    try:
        # 使用规格文件构建
        subprocess.check_call([
            sys.executable, '-m', 'PyInstaller',
            '--clean',
            'main.spec'
        ])
        print("✅ 构建成功！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        return False

def main():
    """主函数"""
    print("=== 商品数据管理工具打包程序 ===")
    
    # 检查必要文件
    required_files = ['main.py', 'templates/index.html', 'key.vdf']
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ 缺少必要文件: {file}")
            return
    
    # 安装依赖
    if not install_requirements():
        print("❌ 依赖安装失败，请手动安装")
        return
    
    # 创建规格文件
    create_spec_file()
    
    # 构建可执行文件
    if build_exe():
        print("\n🎉 打包完成！")
        print("可执行文件位置: dist/商品数据管理工具.exe")
    else:
        print("\n❌ 打包失败，请检查错误信息")

if __name__ == '__main__':
    main()