# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['main_fixed.py'],
    pathex=[],
    binaries=[],
    datas=[('templates', 'templates'), ('key.vdf', '.'), ('settings.ini', '.')],
    hiddenimports=['flask', 'werkzeug', 'jinja2', 'requests', 'PIL', 'PIL.Image', 'pyperclip', 'cryptography', 'cryptography.hazmat.primitives.kdf.pbkdf2', 'cryptography.hazmat.primitives.hashes', 'cryptography.hazmat.primitives.padding', 'cryptography.hazmat.primitives.ciphers', 'cryptography.hazmat.primitives.ciphers.algorithms', 'cryptography.hazmat.primitives.ciphers.modes', 'tkinter', 'tkinter.messagebox'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='商品数据管理工具_新版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
